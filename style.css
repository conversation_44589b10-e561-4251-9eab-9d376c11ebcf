/* style.css - Ayurakshak theme (enhanced green + natural) */

/* Naturopathy-Inspired Color Palette & Typography Variables */
:root {
  /* Primary Nature-Inspired Palette - Softer, more welcoming */
  --primary-green: #4a7c59;
  --primary-light: #6b9b7a;
  --primary-dark: #2d5a3d;
  --sage-green: #87a96b;
  --mint-green: #a8d5ba;

  /* Secondary Earth Tones */
  --earth-brown: #8b7355;
  --earth-light: #b8a082;
  --earth-dark: #6b5b47;
  --warm-beige: #f5f1eb;
  --cream: #faf8f3;

  /* Accent Colors */
  --accent-gold: #d4af37;
  --accent-orange: #e67e22;
  --accent-sage: #9caf88;
  --accent-cream: #f5f5dc;
  --soft-yellow: #f9e79f;

  /* Neutral Palette */
  --white: #ffffff;
  --off-white: #fafafa;
  --light-gray: #f8f9fa;
  --gray: #6c757d;
  --dark-gray: #495057;
  --charcoal: #343a40;
  --black: #212529;
  --text-dark: #2c3e50;
  --text-medium: #34495e;
  --text-light: #7f8c8d;

  /* Background Gradients - More subtle and welcoming */
  --bg-primary: linear-gradient(
    135deg,
    var(--cream) 0%,
    var(--warm-beige) 100%
  );
  --bg-secondary: linear-gradient(
    135deg,
    var(--off-white) 0%,
    var(--light-gray) 100%
  );
  --bg-light: linear-gradient(135deg, var(--white) 0%, var(--cream) 100%);

  /* Hero overlay - much more transparent with gradient */
  --bg-hero: linear-gradient(
    135deg,
    rgba(74, 124, 89, 0.3) 0%,
    rgba(255, 255, 255, 0.1) 50%,
    rgba(74, 124, 89, 0.2) 100%
  );

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  --shadow-green: 0 10px 30px rgba(15, 107, 57, 0.15);
  --shadow-warm: 0 10px 30px rgba(249, 115, 22, 0.15);

  /* Typography */
  --font-heading: "Poppins", system-ui, -apple-system, sans-serif;
  --font-body: "Inter", system-ui, -apple-system, sans-serif;

  /* Spacing */
  --space-xs: 0.25rem;
  --space-sm: 0.5rem;
  --space-md: 1rem;
  --space-lg: 1.5rem;
  --space-xl: 2rem;
  --space-2xl: 3rem;
  --space-3xl: 4rem;

  /* Border Radius */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --radius-2xl: 1.5rem;

  /* Transitions */
  --transition-fast: 0.15s ease-in-out;
  --transition-normal: 0.3s ease-in-out;
  --transition-slow: 0.5s ease-in-out;
}

/* Enhanced Reset & Base Styles */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  scroll-behavior: smooth;
  font-size: 16px;
}

body {
  font-family: var(--font-body);
  font-weight: 400;
  line-height: 1.6;
  color: var(--text-dark);
  background: var(--white);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  overflow-x: hidden;
  min-height: 100vh;
}

/* Typography Scale */
h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: var(--font-heading);
  font-weight: 600;
  line-height: 1.2;
  color: var(--text-dark);
  margin-bottom: var(--space-md);
}

h1 {
  font-size: clamp(2.5rem, 5vw, 4rem);
  font-weight: 700;
}
h2 {
  font-size: clamp(2rem, 4vw, 3rem);
  font-weight: 600;
}
h3 {
  font-size: clamp(1.5rem, 3vw, 2.25rem);
  font-weight: 600;
}
h4 {
  font-size: clamp(1.25rem, 2.5vw, 1.875rem);
  font-weight: 500;
}
h5 {
  font-size: clamp(1.125rem, 2vw, 1.5rem);
  font-weight: 500;
}
h6 {
  font-size: clamp(1rem, 1.5vw, 1.25rem);
  font-weight: 500;
}

p {
  margin-bottom: var(--space-md);
  color: var(--text-medium);
}

.lead {
  font-size: clamp(1.125rem, 2vw, 1.375rem);
  font-weight: 400;
  color: var(--text-light);
}

/* Utility Classes */
.text-gradient {
  background: linear-gradient(135deg, var(--green-600), var(--green-400));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.text-accent {
  color: var(--accent-orange);
}
.text-green {
  color: var(--green-600);
}
.text-muted {
  color: var(--gray-500);
}

/* Enhanced Navigation - Softer, more welcoming */
.nav-glass {
  background: linear-gradient(
    135deg,
    var(--primary-green) 0%,
    var(--sage-green) 100%
  );
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  padding: var(--space-md) var(--space-lg);
  box-shadow: var(--shadow-lg);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  z-index: 1000;
  transition: all var(--transition-normal);
}

.nav-glass.scrolled {
  background: rgba(74, 124, 89, 0.95);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  padding: var(--space-sm) var(--space-lg);
}

.logo-small {
  width: 52px;
  height: 52px;
  object-fit: cover;
  border-radius: var(--radius-xl);
  border: 3px solid rgba(255, 255, 255, 0.2);
  box-shadow: var(--shadow-md);
  transition: all var(--transition-normal);
}

.logo-small:hover {
  transform: scale(1.05);
  box-shadow: var(--shadow-lg);
}

.brand-name {
  font-family: var(--font-heading);
  font-weight: 700;
  color: #fff;
  letter-spacing: 0.5px;
  font-size: 1.25rem;
}

.tagline {
  color: rgba(255, 255, 255, 0.9);
  font-size: 0.75rem;
  font-weight: 400;
  letter-spacing: 0.3px;
}

/* Enhanced Navbar Items */
.navbar-nav .nav-link {
  color: rgba(255, 255, 255, 0.95);
  margin: 0 var(--space-xs);
  font-weight: 500;
  font-size: 0.95rem;
  padding: var(--space-sm) var(--space-md);
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
  position: relative;
}

.navbar-nav .nav-link:hover {
  color: #fff;
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-1px);
}

.navbar-nav .nav-link::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 50%;
  width: 0;
  height: 2px;
  background: var(--green-300);
  transition: all var(--transition-fast);
  transform: translateX(-50%);
}

.navbar-nav .nav-link:hover::after {
  width: 80%;
}

.navbar-nav .btn-donate {
  background: linear-gradient(
    135deg,
    var(--accent-orange),
    var(--accent-amber)
  );
  color: #fff;
  padding: var(--space-sm) var(--space-lg);
  border-radius: var(--radius-lg);
  border: none;
  font-weight: 600;
  box-shadow: var(--shadow-warm);
  transition: all var(--transition-normal);
  margin-left: var(--space-md);
}

.navbar-nav .btn-donate:hover {
  transform: translateY(-2px);
  box-shadow: 0 15px 35px rgba(249, 115, 22, 0.3);
  background: linear-gradient(
    135deg,
    var(--accent-amber),
    var(--accent-orange)
  );
}

.navbar-toggler {
  border: 1px solid rgba(255, 255, 255, 0.2);
  padding: var(--space-xs);
  border-radius: var(--radius-md);
}

.navbar-toggler:focus {
  box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.25);
}

/* Enhanced Hero Section - Fixed height and spacing */
.hero-section {
  position: relative;
  height: 80vh;
  min-height: 700px;
  display: block;
  overflow: hidden;
  margin-bottom: 0;
}

.hero-slide {
  height: 100vh;
  min-height: 600px;
  display: flex;
  align-items: center;
  background-size: cover;
  background-position: center;
  background-attachment: fixed;
  position: relative;
}

.hero-overlay {
  position: absolute;
  inset: 0;
  background: var(--bg-hero);
}

.hero-overlay::before {
  content: "";
  position: absolute;
  inset: 0;
  background: radial-gradient(
    circle at 30% 70%,
    rgba(15, 107, 57, 0.3) 0%,
    transparent 50%
  );
  animation: pulse 4s ease-in-out infinite;
}

@keyframes pulse {
  0%,
  100% {
    opacity: 0.3;
  }
  50% {
    opacity: 0.6;
  }
}

.hero-inner {
  position: relative;
  z-index: 3;
  color: #fff;
  text-align: left;
  max-width: 800px;
  margin-left: 6vw;
  animation: slideInUp 1s ease-out;
  padding: var(--space-xl);
  background: linear-gradient(
    90deg,
    rgba(74, 124, 89, 0.4) 0%,
    rgba(255, 255, 255, 0.1) 70%,
    transparent 100%
  );
  border-radius: var(--radius-xl);
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.hero-inner h1 {
  font-family: var(--font-heading);
  font-size: clamp(2.5rem, 6vw, 4.5rem);
  line-height: 1.1;
  margin-bottom: var(--space-lg);
  font-weight: 800;
  text-shadow: 0 8px 30px rgba(0, 0, 0, 0.3);
  background: linear-gradient(135deg, #fff 0%, rgba(255, 255, 255, 0.9) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hero-inner p.lead {
  font-size: clamp(1.125rem, 2.5vw, 1.5rem);
  color: rgba(255, 255, 255, 0.95);
  margin-bottom: var(--space-2xl);
  line-height: 1.6;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

/* Enhanced CTA Buttons */
.btn-cta {
  background: linear-gradient(135deg, var(--primary-green), var(--sage-green));
  color: #fff;
  border-radius: var(--radius-xl);
  padding: var(--space-lg) var(--space-2xl);
  border: none;
  box-shadow: var(--shadow-md);
  font-weight: 600;
  font-size: 1.1rem;
  text-decoration: none;
  display: inline-block;
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.btn-cta::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.5s;
}

.btn-cta:hover::before {
  left: 100%;
}

.btn-cta:hover {
  transform: translateY(-3px);
  box-shadow: 0 15px 40px rgba(74, 124, 89, 0.3);
  color: #fff;
  background: linear-gradient(135deg, var(--sage-green), var(--primary-green));
}

.btn-outline-cta {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 2px solid rgba(255, 255, 255, 0.3);
  color: #fff;
  border-radius: var(--radius-xl);
  padding: var(--space-lg) var(--space-2xl);
  font-weight: 600;
  font-size: 1.1rem;
  text-decoration: none;
  display: inline-block;
  transition: all var(--transition-normal);
}

.btn-outline-cta:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-3px);
  color: #fff;
}

/* Swiper controls color */
.swiper-button-prev,
.swiper-button-next {
  color: #fff;
}
.swiper-pagination-bullet-active {
  background: #fff;
}

/* ABOUT - Remove gap and improve styling */
section {
  padding: 80px 0;
  margin: 0;
}

/* Remove gap between hero and about */
#about {
  margin-top: -1px;
}

.bg-soft {
  background: var(--bg-primary);
}
.info-card {
  background: var(--white);
  box-shadow: var(--shadow-md);
  border-radius: var(--radius-lg);
  border-left: 4px solid var(--primary-green);
  color: var(--text-dark);
}

/* PROGRAMS - Swiper-based slider with navigation buttons */
.programs-container {
  position: relative;
  overflow: hidden;
}

.program-swiper {
  overflow: visible;
  padding: 20px 0 40px 0;
}

.program-swiper .swiper-slide {
  height: auto;
  display: flex;
}

.program-card {
  width: 100%;
  background: var(--white);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-md);
  overflow: hidden;
  transition: all var(--transition-normal);
  border: 1px solid rgba(74, 124, 89, 0.1);
}

.program-card img {
  width: 100%;
  height: 200px;
  object-fit: cover;
  display: block;
}

.program-card .card-body {
  padding: var(--space-lg);
}

.program-card:hover {
  transform: translateY(-10px);
  box-shadow: var(--shadow-xl);
  border-color: var(--primary-green);
}

/* Program navigation buttons */
.program-nav-btn {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  z-index: 10;
  background: var(--white);
  border: 2px solid var(--primary-green);
  border-radius: 50%;
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--primary-green);
  cursor: pointer;
  transition: all var(--transition-fast);
  box-shadow: var(--shadow-md);
}

.program-nav-btn:hover {
  background: var(--primary-green);
  color: var(--white);
  transform: translateY(-50%) scale(1.1);
}

.program-nav-btn.prev {
  left: -25px;
}

.program-nav-btn.next {
  right: -25px;
}

.program-nav-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.program-nav-btn:disabled:hover {
  background: var(--white);
  color: var(--primary-green);
  transform: translateY(-50%) scale(1);
}

/* Impact cards */
.impact-card {
  background: #fff;
  box-shadow: var(--card-shadow);
  border-radius: 12px;
}

/* Donate CTA */
.cta-donate {
  background: linear-gradient(90deg, var(--primary-green), var(--sage-green));
  padding: 70px 0;
}

/* Enhanced Footer */
.footer {
  background: linear-gradient(
    135deg,
    var(--primary-dark),
    var(--primary-green)
  );
  color: #fff;
  padding: 60px 0 30px;
  position: relative;
}

.footer::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.3),
    transparent
  );
}

.footer-heading {
  color: #fff;
  font-weight: 600;
  margin-bottom: var(--space-md);
  font-size: 1.1rem;
}

.footer-text {
  color: rgba(255, 255, 255, 0.85);
  margin-bottom: var(--space-sm);
  font-size: 0.95rem;
}

.footer-text i {
  color: var(--green-300);
  width: 16px;
}

.social-links {
  display: flex;
  flex-direction: column;
  gap: var(--space-sm);
}

.social-link {
  color: rgba(255, 255, 255, 0.85);
  text-decoration: none;
  transition: all var(--transition-fast);
  font-size: 0.95rem;
  display: flex;
  align-items: center;
}

.social-link:hover {
  color: var(--green-300);
  transform: translateX(5px);
}

.social-link i {
  color: var(--green-300);
  width: 20px;
  margin-right: var(--space-sm);
}

.footer-copyright {
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.9rem;
}

.footer-copyright i {
  color: var(--green-300);
}

.developer-credit {
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: var(--space-xs);
}

.developer-credit i {
  color: var(--green-300);
}

.developer-link {
  color: rgba(255, 255, 255, 0.9);
  text-decoration: none;
  transition: all var(--transition-fast);
  display: inline-flex;
  align-items: center;
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--radius-sm);
  background: rgba(255, 255, 255, 0.1);
}

.developer-link:hover {
  color: #fff;
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
}

.developer-link i {
  color: var(--green-300);
}

/* responsive tweaks */
@media (max-width: 992px) {
  .hero-inner {
    margin-left: 6vw;
    max-width: 640px;
  }
  .hero-inner h1 {
    font-size: 36px;
  }
}
@media (max-width: 768px) {
  .hero-inner {
    margin-left: 4vw;
    max-width: 90%;
    text-align: center;
    margin: auto;
  }
  .hero-inner h1 {
    font-size: 28px;
    text-align: center;
  }
  .program-scroller {
    padding: 8px;
  }
  .program-card {
    min-width: 260px;
  }
}
@media (max-width: 480px) {
  .hero-section {
    height: 68vh;
    min-height: 420px;
  }
  .brand-name {
    font-size: 14px;
  }
  .nav-glass {
    padding: 0.6rem 0.6rem;
  }
}

/* Enhanced Animations and Micro-interactions */
.fade-in-up {
  transform: translateY(18px);
  opacity: 0;
  animation: fadeUp 0.9s ease forwards;
}

@keyframes fadeUp {
  to {
    transform: none;
    opacity: 1;
  }
}

/* Ripple effect for buttons */
.ripple {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.6);
  transform: scale(0);
  animation: ripple-animation 0.6s linear;
  pointer-events: none;
}

@keyframes ripple-animation {
  to {
    transform: scale(4);
    opacity: 0;
  }
}

/* Scroll indicator */
.scroll-indicator {
  text-align: center;
  color: var(--gray-500);
  font-size: 0.875rem;
  margin-top: var(--space-md);
  opacity: 1;
  transition: opacity var(--transition-normal);
}

/* Enhanced card transitions */
.program-card,
.impact-card,
.info-card {
  transition: all var(--transition-normal);
  cursor: pointer;
}

/* Loading states */
.btn-loading {
  position: relative;
  pointer-events: none;
}

.btn-loading::after {
  content: "";
  position: absolute;
  width: 16px;
  height: 16px;
  margin: auto;
  border: 2px solid transparent;
  border-top-color: #ffffff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Floating animation for elements */
.float {
  animation: float 3s ease-in-out infinite;
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

/* Gradient text animation */
.gradient-text {
  background: linear-gradient(
    -45deg,
    var(--green-600),
    var(--green-400),
    var(--accent-orange),
    var(--green-500)
  );
  background-size: 400% 400%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: gradient-shift 4s ease infinite;
}

@keyframes gradient-shift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* Statistics counter styles */
.counter {
  font-family: var(--font-heading);
  font-weight: 700;
  font-size: clamp(2rem, 4vw, 3rem);
  color: var(--green-600);
}

/* Smooth transitions for all interactive elements */
a,
button,
.btn,
.card,
.nav-link {
  transition: all var(--transition-fast);
}

/* Focus states for accessibility */
a:focus,
button:focus,
.btn:focus {
  outline: 2px solid var(--green-400);
  outline-offset: 2px;
}

/* Trust Building Elements */
.trust-badge {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-radius: var(--radius-xl);
  border: 1px solid rgba(15, 107, 57, 0.1);
  transition: all var(--transition-normal);
  height: 100%;
}

.trust-badge:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-lg);
  background: rgba(255, 255, 255, 1);
}

.trust-badge .badge-icon {
  transition: all var(--transition-normal);
}

.trust-badge:hover .badge-icon {
  transform: scale(1.1);
}

.testimonial-card {
  background: #fff;
  border: 1px solid var(--gray-200);
  transition: all var(--transition-normal);
  height: 100%;
}

.testimonial-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-lg);
  border-color: var(--green-300);
}

.testimonial-card .stars {
  font-size: 0.875rem;
}

.testimonial-author strong {
  color: var(--gray-800);
  font-weight: 600;
}

/* Hero Statistics */
.hero-stats .stat-item {
  padding: var(--space-md);
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-radius: var(--radius-lg);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all var(--transition-normal);
}

.hero-stats .stat-item:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateY(-2px);
}

.hero-stats .counter {
  font-size: clamp(1.5rem, 3vw, 2.5rem);
  font-weight: 700;
  color: #fff;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.hero-stats small {
  font-size: 0.75rem;
  font-weight: 500;
}

/* Section Pattern Background */
.section-pattern {
  position: relative;
  overflow: hidden;
}

.section-pattern::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: radial-gradient(
      circle at 25% 25%,
      rgba(15, 107, 57, 0.05) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 75% 75%,
      rgba(249, 115, 22, 0.05) 0%,
      transparent 50%
    );
  pointer-events: none;
}

/* Enhanced Glow Effect */
.glow-on-hover {
  position: relative;
  transition: all var(--transition-normal);
}

.glow-on-hover::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: inherit;
  padding: 2px;
  background: linear-gradient(
    45deg,
    var(--green-400),
    var(--accent-orange),
    var(--green-600)
  );
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask-composite: xor;
  -webkit-mask-composite: xor;
  opacity: 0;
  transition: opacity var(--transition-normal);
}

.glow-on-hover:hover::before {
  opacity: 1;
}

/* Mobile-specific animations */
@media (max-width: 768px) {
  .hero-inner {
    animation: slideInUp 0.8s ease-out;
  }

  .program-card:hover {
    transform: translateY(-5px) scale(1.01);
  }

  .btn-cta,
  .btn-outline-cta {
    padding: var(--space-md) var(--space-xl);
    font-size: 1rem;
  }

  .trust-badge {
    margin-bottom: var(--space-md);
  }

  .hero-stats .counter {
    font-size: clamp(1.25rem, 4vw, 1.75rem);
  }

  .hero-stats small {
    font-size: 0.7rem;
  }
}

/* Enhanced Donation Section */
.donation-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(15px);
  -webkit-backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: var(--shadow-xl);
}

.donation-btn {
  border: 2px solid var(--green-500);
  color: var(--green-600);
  font-weight: 600;
  transition: all var(--transition-fast);
  position: relative;
  overflow: hidden;
}

.donation-btn:hover,
.donation-btn.active {
  background: var(--green-500);
  border-color: var(--green-500);
  color: #fff;
  transform: translateY(-2px);
  box-shadow: var(--shadow-green);
}

.donation-btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.5s;
}

.donation-btn:hover::before {
  left: 100%;
}

.impact-calculator {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-radius: var(--radius-lg);
  padding: var(--space-lg);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.calculator-item {
  background: rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-md);
  padding: var(--space-md);
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all var(--transition-fast);
}

.calculator-item:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateX(5px);
}

.impact-icon {
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
}

.campaign-progress .progress {
  background: rgba(0, 0, 0, 0.1);
  border-radius: var(--radius-md);
}

.campaign-progress .progress-bar {
  background: linear-gradient(90deg, var(--green-500), var(--green-400));
  border-radius: var(--radius-md);
  position: relative;
  overflow: hidden;
}

.campaign-progress .progress-bar::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  background-image: linear-gradient(
    -45deg,
    rgba(255, 255, 255, 0.2) 25%,
    transparent 25%,
    transparent 50%,
    rgba(255, 255, 255, 0.2) 50%,
    rgba(255, 255, 255, 0.2) 75%,
    transparent 75%,
    transparent
  );
  background-size: 30px 30px;
  animation: progress-animation 2s linear infinite;
}

@keyframes progress-animation {
  0% {
    background-position: 0 0;
  }
  100% {
    background-position: 30px 30px;
  }
}

/* Form Enhancements */
.form-control:focus,
.form-select:focus {
  border-color: var(--green-400);
  box-shadow: 0 0 0 0.2rem rgba(52, 168, 83, 0.25);
}

.btn-success {
  background: linear-gradient(135deg, var(--green-500), var(--green-600));
  border: none;
  font-weight: 600;
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.btn-success:hover {
  background: linear-gradient(135deg, var(--green-600), var(--green-700));
  transform: translateY(-2px);
  box-shadow: var(--shadow-green);
}

.btn-success::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.5s;
}

.btn-success:hover::before {
  left: 100%;
}

/* Enhanced Program Cards */
.program-card {
  min-width: 340px;
  max-width: 380px;
  background: #fff;
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-md);
  overflow: hidden;
  scroll-snap-align: center;
  transition: all var(--transition-normal);
  border: 1px solid var(--gray-200);
}

.program-card:hover {
  transform: translateY(-10px);
  box-shadow: var(--shadow-2xl);
  border-color: var(--green-300);
}

.card-image-wrapper {
  position: relative;
  overflow: hidden;
  height: 200px;
}

.card-image-wrapper img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform var(--transition-slow);
}

.program-card:hover .card-image-wrapper img {
  transform: scale(1.05);
}

.card-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    180deg,
    rgba(0, 0, 0, 0.1) 0%,
    rgba(0, 0, 0, 0.3) 70%,
    rgba(0, 0, 0, 0.6) 100%
  );
  display: flex;
  align-items: flex-end;
  padding: var(--space-md);
  opacity: 0;
  transition: opacity var(--transition-normal);
}

.program-card:hover .card-overlay {
  opacity: 1;
}

.stat-badge {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  color: var(--gray-800);
  padding: var(--space-xs) var(--space-md);
  border-radius: var(--radius-lg);
  font-size: 0.875rem;
  font-weight: 600;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.card-header-section {
  display: flex;
  align-items: center;
  gap: var(--space-md);
  margin-bottom: var(--space-md);
}

.program-icon {
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, var(--green-500), var(--green-400));
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 1.25rem;
  box-shadow: var(--shadow-md);
  transition: all var(--transition-normal);
}

.program-card:hover .program-icon {
  transform: scale(1.1) rotate(5deg);
  box-shadow: var(--shadow-lg);
}

.card-progress {
  background: var(--gray-50);
  border-radius: var(--radius-md);
  padding: var(--space-md);
  border: 1px solid var(--gray-200);
}

.progress-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: var(--space-xs);
  font-size: 0.75rem;
  color: var(--gray-600);
  font-weight: 500;
}

.card-progress .progress {
  background: var(--gray-200);
  border-radius: var(--radius-sm);
  overflow: hidden;
}

.card-progress .progress-bar {
  border-radius: var(--radius-sm);
  transition: width var(--transition-slow);
}

.card-actions {
  display: flex;
  gap: var(--space-sm);
  margin-top: var(--space-md);
}

.card-actions .btn {
  flex: 1;
  font-size: 0.875rem;
  padding: var(--space-sm) var(--space-md);
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
}

.card-actions .btn-outline-success {
  border: 2px solid var(--green-500);
  color: var(--green-600);
  background: transparent;
}

.card-actions .btn-outline-success:hover {
  background: var(--green-500);
  color: #fff;
  transform: translateY(-1px);
}

.card-actions .btn-success {
  background: linear-gradient(135deg, var(--green-500), var(--green-600));
  border: none;
  color: #fff;
}

.card-actions .btn-success:hover {
  background: linear-gradient(135deg, var(--green-600), var(--green-700));
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

/* Program Card Responsive */
@media (max-width: 768px) {
  .program-card {
    min-width: 280px;
    max-width: 320px;
  }

  .card-image-wrapper {
    height: 160px;
  }

  .program-icon {
    width: 40px;
    height: 40px;
    font-size: 1rem;
  }

  .card-actions {
    flex-direction: column;
  }

  .card-actions .btn {
    flex: none;
  }
}

/* Final Responsive Enhancements */
@media (max-width: 576px) {
  .hero-stats .row {
    gap: var(--space-sm);
  }

  .hero-stats .col-4 {
    flex: 0 0 100%;
    max-width: 100%;
    margin-bottom: var(--space-sm);
  }

  .trust-badge {
    padding: var(--space-md);
    margin-bottom: var(--space-lg);
  }

  .testimonial-card {
    margin-bottom: var(--space-lg);
  }

  .donation-card {
    margin-top: var(--space-lg);
  }

  .impact-calculator {
    padding: var(--space-md);
  }

  .calculator-item {
    padding: var(--space-sm);
    margin-bottom: var(--space-sm);
  }

  .hero-actions {
    flex-direction: column;
    gap: var(--space-md);
  }

  .hero-actions .btn {
    width: 100%;
    margin: 0;
  }
}

/* Touch-friendly interactions */
@media (hover: none) and (pointer: coarse) {
  .program-card:hover,
  .trust-badge:hover,
  .testimonial-card:hover {
    transform: none;
  }

  .program-card:active {
    transform: scale(0.98);
  }

  .btn:active {
    transform: scale(0.95);
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  :root {
    --green-600: #0d5d32;
    --green-500: #177a3e;
    --gray-800: #000000;
    --gray-700: #1a1a1a;
  }

  .btn-outline-success {
    border-width: 3px;
  }

  .nav-link::after {
    height: 3px;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }

  .hero-slide {
    background-attachment: scroll;
  }
}

/* Print styles */
@media print {
  .nav-glass,
  .hero-section,
  .btn,
  .card-actions {
    display: none !important;
  }

  body {
    background: white !important;
    color: black !important;
  }

  .section-pattern::before {
    display: none;
  }
}

/* Additional improvements for text visibility and color scheme */
.card {
  border: 1px solid rgba(74, 124, 89, 0.1);
  color: var(--text-dark);
}

.card-body {
  color: var(--text-dark);
}

.card-title {
  color: var(--text-dark);
  font-weight: 600;
}

.card-text {
  color: var(--text-medium);
}

/* Better section backgrounds for contrast */
.bg-light {
  background: var(--bg-light) !important;
}

.bg-white {
  background: var(--white) !important;
}

/* Improve text visibility in all sections */
section {
  color: var(--text-dark);
}

section h1,
section h2,
section h3,
section h4,
section h5,
section h6 {
  color: var(--text-dark);
}

section p {
  color: var(--text-medium);
}

/* Fix program navigation for mobile */
@media (max-width: 768px) {
  .program-nav-btn {
    display: none;
  }
}
