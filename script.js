// Enhanced script.js - Advanced animations and interactions
document.addEventListener("DOMContentLoaded", function () {
  // Enhanced AOS initialization
  if (window.AOS) {
    AOS.init({
      duration: 1000,
      once: true,
      easing: "ease-out-cubic",
      offset: 100,
      delay: 100,
    });
  }

  // Enhanced Swiper with manual controls only
  if (window.Swiper) {
    // Hero Swiper
    const heroSwiper = new Swiper(".mySwiper", {
      loop: true,
      effect: "slide",
      speed: 800,
      autoplay: false, // Disabled autoplay for manual control
      pagination: {
        el: ".swiper-pagination",
        clickable: true,
        dynamicBullets: false,
      },
      navigation: {
        nextEl: ".swiper-button-next",
        prevEl: ".swiper-button-prev",
      },
      on: {
        slideChange: function () {
          // Add slide change animations
          const activeSlide = this.slides[this.activeIndex];
          const content = activeSlide.querySelector(".hero-inner");
          if (content) {
            content.style.animation = "none";
            setTimeout(() => {
              content.style.animation = "slideInUp 0.8s ease-out";
            }, 50);
          }
        },
      },
    });

    // Programs Swiper
    const programSwiper = new Swiper(".program-swiper", {
      slidesPerView: 1,
      spaceBetween: 30,
      speed: 600,
      autoplay: false,
      loop: true,
      breakpoints: {
        768: {
          slidesPerView: 2,
        },
        1024: {
          slidesPerView: 3,
        },
      },
    });

    // Custom navigation for programs
    const programPrev = document.getElementById("programPrev");
    const programNext = document.getElementById("programNext");

    if (programPrev && programNext) {
      programPrev.addEventListener("click", () => {
        programSwiper.slidePrev();
      });

      programNext.addEventListener("click", () => {
        programSwiper.slideNext();
      });
    }
  }

  // Enhanced navbar scroll effect
  const navbar = document.querySelector(".nav-glass");
  let lastScrollY = window.scrollY;

  window.addEventListener("scroll", () => {
    const currentScrollY = window.scrollY;

    if (currentScrollY > 100) {
      navbar.classList.add("scrolled");
    } else {
      navbar.classList.remove("scrolled");
    }

    // Hide/show navbar on scroll
    if (currentScrollY > lastScrollY && currentScrollY > 200) {
      navbar.style.transform = "translateY(-100%)";
    } else {
      navbar.style.transform = "translateY(0)";
    }

    lastScrollY = currentScrollY;
  });

  // Program slider is now handled by Swiper above

  // Animated counters for statistics
  function animateCounters() {
    const counters = document.querySelectorAll(".counter");
    counters.forEach((counter) => {
      const target = parseInt(counter.getAttribute("data-target"));
      const duration = 2000;
      const increment = target / (duration / 16);
      let current = 0;

      const updateCounter = () => {
        current += increment;
        if (current < target) {
          counter.textContent = Math.floor(current).toLocaleString();
          requestAnimationFrame(updateCounter);
        } else {
          counter.textContent = target.toLocaleString();
        }
      };

      updateCounter();
    });
  }

  // Intersection Observer for counter animation
  const counterObserver = new IntersectionObserver((entries) => {
    entries.forEach((entry) => {
      if (entry.isIntersecting) {
        animateCounters();
        counterObserver.unobserve(entry.target);
      }
    });
  });

  const statsSection = document.querySelector(".stats-section");
  if (statsSection) {
    counterObserver.observe(statsSection);
  }

  // Enhanced smooth scroll for internal links
  document.querySelectorAll('a[href^="#"]').forEach((anchor) => {
    anchor.addEventListener("click", function (e) {
      const target = document.querySelector(this.getAttribute("href"));
      if (target) {
        e.preventDefault();

        // Add offset for fixed navbar
        const navbarHeight = navbar.offsetHeight;
        const targetPosition = target.offsetTop - navbarHeight;

        window.scrollTo({
          top: targetPosition,
          behavior: "smooth",
        });
      }
    });
  });

  // Add loading animation
  const addLoadingStates = () => {
    const buttons = document.querySelectorAll(".btn-cta, .btn-outline-cta");
    buttons.forEach((btn) => {
      btn.addEventListener("click", function (e) {
        if (this.href && this.href.includes("#")) return; // Skip for anchor links

        this.style.position = "relative";
        this.style.overflow = "hidden";

        const ripple = document.createElement("span");
        ripple.className = "ripple";
        this.appendChild(ripple);

        setTimeout(() => {
          ripple.remove();
        }, 600);
      });
    });
  };

  addLoadingStates();

  // Parallax effect for hero background
  window.addEventListener("scroll", () => {
    const scrolled = window.pageYOffset;
    const heroSlides = document.querySelectorAll(".hero-slide");

    heroSlides.forEach((slide) => {
      const rate = scrolled * -0.5;
      slide.style.transform = `translateY(${rate}px)`;
    });
  });

  // Add hover effects to cards
  const addCardHoverEffects = () => {
    const cards = document.querySelectorAll(
      ".program-card, .impact-card, .info-card"
    );

    cards.forEach((card) => {
      card.addEventListener("mouseenter", function () {
        this.style.transform = "translateY(-10px) scale(1.02)";
        this.style.boxShadow = "0 20px 40px rgba(0, 0, 0, 0.15)";
      });

      card.addEventListener("mouseleave", function () {
        this.style.transform = "translateY(0) scale(1)";
        this.style.boxShadow = "";
      });
    });
  };

  addCardHoverEffects();

  // Enhanced donation functionality
  const initDonationFeatures = () => {
    // Donation amount selection
    const donationBtns = document.querySelectorAll(".donation-btn");
    const customAmountInput = document.getElementById("customAmount");
    let selectedAmount = 0;

    donationBtns.forEach((btn) => {
      btn.addEventListener("click", function () {
        // Remove active class from all buttons
        donationBtns.forEach((b) => b.classList.remove("active"));

        // Add active class to clicked button
        this.classList.add("active");

        // Set selected amount
        selectedAmount = parseInt(this.getAttribute("data-amount"));

        // Clear custom input
        if (customAmountInput) {
          customAmountInput.value = "";
        }

        // Update impact display
        updateImpactDisplay(selectedAmount);
      });
    });

    // Custom amount input
    if (customAmountInput) {
      customAmountInput.addEventListener("input", function () {
        // Remove active class from all buttons
        donationBtns.forEach((btn) => btn.classList.remove("active"));

        // Set selected amount
        selectedAmount = parseInt(this.value) || 0;

        // Update impact display
        updateImpactDisplay(selectedAmount);
      });
    }

    // Update impact display based on amount
    function updateImpactDisplay(amount) {
      const calculatorItems = document.querySelectorAll(".calculator-item");

      calculatorItems.forEach((item) => {
        const text = item.querySelector("span").textContent;
        const value = parseInt(text.match(/₹(\d+)/)[1]);
        const impact = Math.floor(amount / value);

        // Add visual feedback
        if (impact > 0) {
          item.style.background = "rgba(255, 255, 255, 0.2)";
          item.style.borderColor = "rgba(255, 255, 255, 0.3)";

          // Add impact count
          let impactCount = item.querySelector(".impact-count");
          if (!impactCount) {
            impactCount = document.createElement("span");
            impactCount.className =
              "impact-count badge bg-light text-dark ms-2";
            item.querySelector("span").appendChild(impactCount);
          }
          impactCount.textContent = `×${impact}`;
        } else {
          item.style.background = "rgba(255, 255, 255, 0.1)";
          item.style.borderColor = "rgba(255, 255, 255, 0.1)";

          const impactCount = item.querySelector(".impact-count");
          if (impactCount) {
            impactCount.remove();
          }
        }
      });
    }

    // Store selected amount globally for processing
    window.selectedDonationAmount = () => selectedAmount;
  };

  initDonationFeatures();

  // Donation processing function
  window.processDonation = () => {
    const amount = window.selectedDonationAmount();
    const purpose = document.getElementById("donationPurpose").value;

    if (amount <= 0) {
      alert("Please select a donation amount");
      return;
    }

    // Show loading state
    const btn = document.querySelector(".btn-success");
    const originalText = btn.innerHTML;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Processing...';
    btn.disabled = true;

    // Simulate payment processing
    setTimeout(() => {
      alert(
        `Thank you for your donation of ₹${amount.toLocaleString()} for ${purpose}!\n\nYou will be redirected to the secure payment gateway.`
      );

      // Reset button
      btn.innerHTML = originalText;
      btn.disabled = false;

      // In a real implementation, redirect to payment gateway
      // window.location.href = `payment-gateway.html?amount=${amount}&purpose=${purpose}`;
    }, 2000);
  };

  // Modal function for donation
  window.openDonationModal = () => {
    // Scroll to donation section
    document.getElementById("donate").scrollIntoView({
      behavior: "smooth",
      block: "center",
    });
  };

  // Program card interactions
  window.learnMore = (program) => {
    const programInfo = {
      health: {
        title: "Health Camps Program",
        description:
          "Our health camps bring traditional Ayurvedic medicine to remote villages across India. We provide free consultations, herbal medicines, and wellness workshops to communities that lack access to healthcare.",
        impact:
          "Over 2,500 people have received treatment through our health camps, with 85% reporting significant improvement in their conditions.",
        cost: "₹500 sponsors one person's complete health camp visit including consultation and medicines.",
      },
      gardens: {
        title: "Community Gardens Initiative",
        description:
          "We establish nutrition gardens in villages to ensure food security and teach sustainable agriculture practices. These gardens provide fresh vegetables and herbs for families while promoting environmental conservation.",
        impact:
          "150+ gardens established, feeding over 1,000 families and reducing their food expenses by 40%.",
        cost: "₹2,000 helps establish one complete community garden with seeds, tools, and training.",
      },
      education: {
        title: "Education & Skills Development",
        description:
          "We provide school supplies, digital literacy training, and vocational skills to children and adults. Our programs focus on practical skills that lead to sustainable employment opportunities.",
        impact:
          "800+ students have benefited from our education programs, with 70% of vocational training graduates finding employment.",
        cost: "₹1,500 sponsors one student's complete education kit and training for one year.",
      },
      relief: {
        title: "Emergency Disaster Relief",
        description:
          "When disasters strike, we provide immediate relief including food, clean water, hygiene kits, and emergency medical supplies. Our rapid response team ensures help reaches affected communities quickly.",
        impact:
          "Provided emergency relief to 5,000+ families during floods and natural disasters across multiple states.",
        cost: "₹2,500 provides one family's complete emergency relief kit for one month.",
      },
      livelihoods: {
        title: "Women's Livelihood Program",
        description:
          "We train women in herbal product manufacturing, sustainable crafts, and micro-entrepreneurship. Our program includes seed funding, mentorship, and market linkages for sustainable income generation.",
        impact:
          "300+ women entrepreneurs now earn an average of ₹8,000 monthly, achieving financial independence.",
        cost: "₹5,000 provides complete training and seed funding for one woman entrepreneur.",
      },
    };

    const info = programInfo[program];
    if (info) {
      alert(
        `${info.title}\n\n${info.description}\n\nImpact: ${info.impact}\n\nHow to help: ${info.cost}`
      );
    }
  };

  window.donateToProgram = (program) => {
    // Set the donation purpose and scroll to donation section
    const purposeSelect = document.getElementById("donationPurpose");
    if (purposeSelect) {
      const purposeMap = {
        health: "health",
        gardens: "general",
        education: "education",
        relief: "emergency",
        livelihoods: "livelihoods",
      };
      purposeSelect.value = purposeMap[program] || "general";
    }

    // Scroll to donation section
    document.getElementById("donate").scrollIntoView({
      behavior: "smooth",
      block: "center",
    });

    // Highlight the donation section briefly
    const donateSection = document.getElementById("donate");
    donateSection.style.animation = "pulse 2s ease-in-out";
    setTimeout(() => {
      donateSection.style.animation = "";
    }, 2000);
  };
});
